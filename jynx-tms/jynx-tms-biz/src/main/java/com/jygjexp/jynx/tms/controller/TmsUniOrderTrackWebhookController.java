package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.uniuni.UniUniTrackingDataDTO;
import com.jygjexp.jynx.tms.dto.uniuni.UniUniPodDataDTO;
import com.jygjexp.jynx.tms.service.TmsUniOrderWebhookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: daiyuxuan
 * @create: 2025/9/6
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/pullTracking")
@Tag(description = "pullTracking", name = "uni订单回调接口管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsUniOrderTrackWebhookController {

    private final TmsUniOrderWebhookService uniOrderWebhookService;

    /**
     * 批量处理轨迹数据
     *
     * @param uniUniTrackingDataDTOList
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "批量处理轨迹数据")
    @PostMapping("/tmsUniOrderTrack")
    public R tmsUniOrderTrack(@RequestBody List<UniUniTrackingDataDTO> uniUniTrackingDataDTOList) {
        if (CollUtil.isEmpty(uniUniTrackingDataDTOList)) {
            return R.failed("轨迹数据为空");
        }

        return R.ok(uniOrderWebhookService.tmsUniOrderTrack(uniUniTrackingDataDTOList));
    }

    @Inner(value = false)
    @Operation(summary = "POD回调数据")
    @PostMapping("/tmsUniOrderPod")
    public R tmsUniOrderPod(@RequestBody UniUniPodDataDTO uniUniPodDataDTO) {
        return R.ok(uniOrderWebhookService.tmsUniOrderPod(uniUniPodDataDTO));
    }

}