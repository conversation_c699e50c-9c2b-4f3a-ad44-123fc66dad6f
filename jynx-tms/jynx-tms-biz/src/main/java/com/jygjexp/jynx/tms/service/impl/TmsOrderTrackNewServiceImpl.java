package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.constants.TrackTypeConstant;
import com.jygjexp.jynx.tms.dto.PushNodeMsgDto;
import com.jygjexp.jynx.tms.dto.TmsApiOrderTaskDto;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import com.jygjexp.jynx.tms.entity.TmsTrackWebhookEntity;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsOrderTrackMapper;
import com.jygjexp.jynx.tms.mongo.entity.TmsLargeDriverRealTimeLocation;
import com.jygjexp.jynx.tms.mongo.entity.TmsOrderTrackText;
import com.jygjexp.jynx.tms.mongo.service.TmsTrackTextService;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackNewService;
import com.jygjexp.jynx.tms.service.TmsTrackWebhookService;
import com.jygjexp.jynx.tms.vo.TmsLargeDriverRealTimeLocationVo;
import com.jygjexp.jynx.tms.vo.TmsWebOrderTrackVo;
import com.jygjexp.jynx.tms.utils.TemuApiClient;
import com.mongoplus.conditions.query.QueryWrapper;
import com.mongoplus.conditions.update.LambdaUpdateChainWrapper;
import org.springframework.beans.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: 中大件轨迹接口装载方法接口
 * @Date: 2025/8/19 10:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsOrderTrackNewServiceImpl implements TmsOrderTrackNewService {

    private final TmsCustomerOrderService customerOrderService;
    private final TmsOrderTrackMapper orderTrackMapper;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsTrackTextService tmsTrackTextService;
    private final TemuApiClient temuApiClient;
    private final TmsTrackWebhookService tmsTrackWebhookService;

    // 佳邮轨迹查询接口配置
    private static final String JY_API_URL = "http://api.jygjexp.com/v1/api/tracking/query/trackNB";
    private static final String JY_API_KEY = "675bfe2fd67105e9a88e564bf0f0344c";


    // ==================== 迁移自zxoms的官网轨迹查询接口实现 ====================

    /**
     * 批量订单轨迹查询 - 根据不同单号类型实现不同的轨迹查询策略
     * @param pkgNos 包裹号列表，逗号分隔
     * @param zipInput 邮编（可选）
     * @return 批量轨迹查询结果
     */
    @Override
    public R getTracksFromZxoms(String pkgNos, String zipInput) {
        if (StrUtil.isBlank(pkgNos)) {
            return R.failed("500300", "pkgNo can not be empty");
        }

        log.info("TMS批量轨迹查询开始，包裹号：{}", pkgNos);

        int maxSize = 50;
        String[] pkgNoArr = pkgNos.trim().split(",");
        if (pkgNoArr.length > maxSize) {
            return R.failed("500301", "Query up to " + maxSize + " packages at a time");
        }

        JSONArray ja = new JSONArray(); // 最终轨迹结果集合
        Set<String> handledPkgNos = new HashSet<>(); // 防止重复添加
        List<String> pkgNoList = Arrays.stream(pkgNoArr).map(String::trim).filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(pkgNoList)) {
            return R.failed("500300", "No valid package numbers provided");
        }

        // 根据单号类型分类处理
        List<String> nOrderNos = new ArrayList<>(); // N开头：内部单号
        List<String> jyOrderNos = new ArrayList<>(); // JY开头：外部单号
        List<String> gvOrderNos = new ArrayList<>(); // GV开头：外部单号
        List<String> u9999OrderNos = new ArrayList<>(); // U9999开头：外部单号

        for (String pkgNo : pkgNoList) {
            if (pkgNo.startsWith("N")) {
                nOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("JYB")) {
                // 将 JYB 开头的单号按 N 开头单号处理(大货系统)
                nOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("JY")) {
                // 处理 JY 开头的单号（小包系统）
                jyOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("GV")) {
                gvOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("U9999")) {
                // 处理 U9999 开头的单号（UNIUNI系统）
                u9999OrderNos.add(pkgNo);
            } else {
                // 其他类型单号按 N 开头处理
                nOrderNos.add(pkgNo);
            }
        }

        // 处理N开头单号：调用TmsOrderTrackNew服务
        if (!nOrderNos.isEmpty()) {
            processNOrderNos(nOrderNos, zipInput, ja, handledPkgNos);
        }

        // 处理JY/GV/U9999开头单号：先查本系统，条件不满足则查佳邮接口
        List<String> externalOrderNos = new ArrayList<>();
        externalOrderNos.addAll(jyOrderNos);
        externalOrderNos.addAll(gvOrderNos);
        externalOrderNos.addAll(u9999OrderNos);

        if (!externalOrderNos.isEmpty()) {
            processExternalOrderNos(externalOrderNos, zipInput, ja, handledPkgNos);
        }

        log.info("TMS批量轨迹查询成功，查询包裹数：{}，返回结果数：{}", pkgNoList.size(), ja.size());
        return R.ok(ja);
    }



    /**
     * 处理JY/GV/U9999开头单号：先查本系统，条件不满足则查佳邮接口
     * @param externalOrderNos 外部订单号列表
     * @param zipInput 邮编
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void processExternalOrderNos(List<String> externalOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        // 通过jyOrderNo或customerOrderNumber字段查询本系统是否存在该单号
        List<TmsCustomerOrderEntity> orders = customerOrderService.list(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                        .eq(TmsCustomerOrderEntity::getSubFlag,false)
                        .and(wrapper -> wrapper
                                .in(TmsCustomerOrderEntity::getJyOrderNo, externalOrderNos)
                                .or()
                                .in(TmsCustomerOrderEntity::getCustomerOrderNumber, externalOrderNos)));

        // 处理邮编过滤逻辑
        // 获取到邮编列表
        List<String> zipList = Arrays.stream(StrUtil.nullToEmpty(zipInput).split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(String::toUpperCase)
                .collect(Collectors.toList());

        // 检查是否订单含有目标邮编
        boolean hasZipMatch = CollUtil.isNotEmpty(zipList) && orders.stream().anyMatch(order -> {
            String destZip = order.getDestPostalCode();
            return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
        });

        // 记录NB系统轨迹数据充足的订单
        Set<String> sufficientLocalTrackOrders = new HashSet<>();

        // 从查询到的订单中提取委托单号并去重
        List<String> entrustedOrderNumbers = orders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询轨迹数据，因为jy、gv、u9999是一票一件，所以这个写法是根据一票一件的写法
        Map<String, List<TmsOrderTrackEntity>> trackMap = new HashMap<>();
        for (String entrustedOrderNo : entrustedOrderNumbers) {
            String mainOrderNo = entrustedOrderNo;
            if (mainOrderNo != null && mainOrderNo.length() > 15) {
                mainOrderNo = mainOrderNo.substring(0, mainOrderNo.length() - 3);
            }

            List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(
                    new LambdaQueryWrapper<TmsOrderTrackEntity>()
                            .eq(TmsOrderTrackEntity::getDelFlag, "0")
                            .eq(TmsOrderTrackEntity::getOrderNo, mainOrderNo+"001")
                            .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                            .orderByDesc(TmsOrderTrackEntity::getAddTime));

            if (CollUtil.isNotEmpty(tracks)) {
                trackMap.put(entrustedOrderNo, tracks);
            }
        }

        // 判断本系统轨迹数据
        for (String orderNo : externalOrderNos) {
            // 查找匹配的订单
            TmsCustomerOrderEntity matchedOrder = orders.stream()
                    .filter(order -> orderNo.equals(order.getJyOrderNo()) || orderNo.equals(order.getCustomerOrderNumber()))
                    .findFirst()
                    .orElse(null);

            if (matchedOrder != null) {
                // 邮编过滤
                if (hasZipMatch) {
                    String destZip = StrUtil.blankToDefault(matchedOrder.getDestPostalCode(), "").toUpperCase();
                    if (!zipList.contains(destZip)) {
                        continue;
                    }
                }

                // 获取该订单对应的轨迹数据
                String entrustedOrderNo = matchedOrder.getEntrustedOrderNumber();
                List<TmsOrderTrackEntity> tracks = trackMap.get(entrustedOrderNo);

                // 如果本系统轨迹条数 >= 2条，使用本系统轨迹
                if (CollUtil.isNotEmpty(tracks) && tracks.size() >= 2) {
                    JSONObject orderJo = new JSONObject();
                    orderJo.set("pkgNo", orderNo);
                    orderJo.set("orderStatus", matchedOrder.getOrderStatus());
                    orderJo.set("destination", matchedOrder.getDestination());

                    // 转换轨迹数据格式
                    JSONArray trackJa = tracks.stream()
                            .filter(track -> track.getLocationDescription() != null)
                            .map(track -> {
                                JSONObject jo = new JSONObject();
                                jo.set("code", track.getStatusCode());
                                jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd'T'HH:mm:ss"));
                                jo.set("timezone", StrUtil.blankToDefault(track.getTimeZone(), ""));
                                jo.set("status", StrUtil.blankToDefault(track.getOrderStatus(), ""));
                                jo.set("city", StrUtil.blankToDefault(track.getCity(), ""));
                                jo.set("content", StrUtil.blankToDefault(track.getExternalDescription(), ""));
                                return jo;
                            }).collect(Collectors.toCollection(JSONArray::new));

                    orderJo.set("track", trackJa);

                    // 根据邮编判断是否返回签收图
                    if (hasZipMatch && StrUtil.isNotBlank(matchedOrder.getDeliveryProof())) {
                        orderJo.set("images", Arrays.asList(matchedOrder.getDeliveryProof()));
                    }

                    ja.add(orderJo);
                    handledPkgNos.add(orderNo);
                    sufficientLocalTrackOrders.add(orderNo);
                    log.info("外部订单使用TMS本地轨迹：{}", orderNo);
                }
            }
        }

        // 佳邮接口兜底逻辑
        List<String> needJyApiOrders = externalOrderNos.stream()
                .filter(orderNo -> !sufficientLocalTrackOrders.contains(orderNo))
                .collect(Collectors.toList());

        if (!needJyApiOrders.isEmpty()) {
            log.info("外部订单号在本地系统轨迹数据不足，调用佳邮接口查询，订单号：{}", needJyApiOrders);
            queryJyApiTracksWithFallback(needJyApiOrders, ja, handledPkgNos, orders, zipList, hasZipMatch);
        }
    }



    /**
     * 调用佳邮接口查询轨迹（兜底）
     * @param orderNos 订单号列表
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param localOrders 本地订单数据
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     */
    private void queryJyApiTracksWithFallback(List<String> orderNos, JSONArray ja, Set<String> handledPkgNos,
                                              List<TmsCustomerOrderEntity> localOrders, List<String> zipList, boolean hasZipMatch) {
        if (CollUtil.isEmpty(orderNos)) {
            return;
        }

        try {
            // 构建请求
            HttpRequest request = HttpRequest.post(JY_API_URL);
            request.header("apiKey", JY_API_KEY);
            request.body(JSONUtil.toJsonStr(orderNos));

            // 发送请求
            HttpResponse response = request.execute();
            String result = response.body();
            log.info("佳邮接口返回结果：{}", result);

            JSONObject retJo = JSONUtil.parseObj(result);
            if (retJo.getInt("code") == 1) {
                JSONArray dataArray = retJo.getJSONArray("data");
                if (dataArray != null && !dataArray.isEmpty()) {
                    for (Object item : dataArray) {
                        JSONObject tracks = (JSONObject) item;
                        // 根据实际返回格式获取订单号
                        String pkgNo = tracks.getStr("trackingNo");
                        if (StrUtil.isBlank(pkgNo)) {
                            pkgNo = tracks.getStr("logisticsServiceNumber");
                        }

                        // 获取轨迹详情数组
                        JSONArray fromDetailArray = tracks.getJSONArray("fromDetail");

                        // 检查佳邮轨迹是否有足够数据（≥2条）
                        if (fromDetailArray != null && fromDetailArray.size() >= 2) {
                            JSONObject orderJo = new JSONObject();
                            orderJo.set("pkgNo", pkgNo);

                            // 转换轨迹格式以匹配统一返回格式
                            JSONArray trackJa = new JSONArray();
                            for (Object trackItem : fromDetailArray) {
                                JSONObject trackDetail = (JSONObject) trackItem;
                                JSONObject trackJo = new JSONObject();
                                trackJo.set("code", trackDetail.getStr("pathCode"));
                                trackJo.set("time", trackDetail.getStr("pathTime"));
                                trackJo.set("status", tracks.getStr("status"));
                                trackJo.set("city", trackDetail.getStr("pathLocation"));
                                trackJo.set("timezone", trackDetail.getStr("timezone"));
                                trackJo.set("content", trackDetail.getStr("pathInfo"));
                                trackJa.add(trackJo);
                            }
                            orderJo.set("track", trackJa);

                            // 处理POD签收图片 - 使用pods字段
                            JSONArray podsArray = tracks.getJSONArray("pods");
                            if (podsArray != null && !podsArray.isEmpty()) {
                                List<String> imageUrls = new ArrayList<>();
                                for (Object pod : podsArray) {
                                    if (pod instanceof String) {
                                        String imageUrl = (String) pod;
                                        if (StrUtil.isNotBlank(imageUrl)) {
                                            imageUrls.add(imageUrl);
                                        }
                                    }
                                }
                                if (!imageUrls.isEmpty()) {
                                    orderJo.set("images", imageUrls);
                                }
                            }

                            ja.add(orderJo);
                            handledPkgNos.add(pkgNo);
                            log.info("佳邮接口查询成功，订单号：{}，轨迹数量：{}", pkgNo, fromDetailArray.size());
                        } else {
                            // 佳邮轨迹数据不足，使用本系统轨迹作为兜底
                            log.info("佳邮接口轨迹数据不足（<2条），使用本系统轨迹作为兜底，订单号：{}，轨迹数量：{}",
                                    pkgNo, fromDetailArray != null ? fromDetailArray.size() : 0);
                            useLocalTrackAsFallback(pkgNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
                        }
                    }
                }
            } else {
                log.error("佳邮接口返回错误：{}", retJo.getStr("message"));
                // 佳邮接口失败，使用本系统轨迹作为兜底
                for (String orderNo : orderNos) {
                    useLocalTrackAsFallback(orderNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
                }
            }

        } catch (Exception e) {
            log.error("调用佳邮接口异常，订单号：{}", orderNos, e);
            // 异常情况下使用本系统轨迹作为兜底
            for (String orderNo : orderNos) {
                useLocalTrackAsFallback(orderNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
            }
        }
    }

    /**
     * 使用NB系统轨迹作为兜底
     * @param orderNo 订单号
     * @param localOrders 本地订单数据
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void useLocalTrackAsFallback(String orderNo, List<TmsCustomerOrderEntity> localOrders,
                                         List<String> zipList, boolean hasZipMatch, JSONArray ja, Set<String> handledPkgNos) {
        // 查找匹配的订单
        TmsCustomerOrderEntity matchedOrder = localOrders.stream()
                .filter(order -> orderNo.equals(order.getJyOrderNo()) || orderNo.equals(order.getCustomerOrderNumber()))
                .findFirst()
                .orElse(null);

        if (matchedOrder != null) {
            // 邮编过滤
            if (hasZipMatch) {
                String destZip = StrUtil.blankToDefault(matchedOrder.getDestPostalCode(), "").toUpperCase();
                if (!zipList.contains(destZip)) {
                    return;
                }
            }

            // 查询轨迹数据
            String mainOrderNo = matchedOrder.getEntrustedOrderNumber();
            if (mainOrderNo != null && mainOrderNo.length() > 15) {
                mainOrderNo = mainOrderNo.substring(0, mainOrderNo.length() - 3);
            }

            List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(
                    new LambdaQueryWrapper<TmsOrderTrackEntity>()
                            .eq(TmsOrderTrackEntity::getDelFlag, "0")
                            .likeRight(TmsOrderTrackEntity::getOrderNo, mainOrderNo+"001")
                            .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                            .orderByDesc(TmsOrderTrackEntity::getAddTime));

            if (CollUtil.isNotEmpty(tracks)) {
                JSONObject orderJo = new JSONObject();
                orderJo.set("pkgNo", orderNo);
                orderJo.set("orderStatus", matchedOrder.getOrderStatus());
                orderJo.set("destination", matchedOrder.getDestination());

                // 转换轨迹数据格式
                JSONArray trackJa = tracks.stream()
                        .filter(track -> track.getLocationDescription() != null)
                        .map(track -> {
                            JSONObject jo = new JSONObject();
                            jo.set("code", track.getStatusCode());
                            jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd'T'HH:mm:ss"));
                            jo.set("timezone",StrUtil.blankToDefault(track.getTimeZone(), ""));
                            jo.set("status", StrUtil.blankToDefault(track.getOrderStatus(), ""));
                            jo.set("city", StrUtil.blankToDefault(track.getCity(), ""));
                            jo.set("content", StrUtil.blankToDefault(track.getExternalDescription(), ""));
                            return jo;
                        }).collect(Collectors.toCollection(JSONArray::new));

                orderJo.set("track", trackJa);

                // 根据邮编判断是否返回签收图
                if (hasZipMatch && StrUtil.isNotBlank(matchedOrder.getDeliveryProof())) {
                    orderJo.set("images", Arrays.asList(matchedOrder.getDeliveryProof()));
                }

                ja.add(orderJo);
                handledPkgNos.add(orderNo);
            }
        }
    }



    // 原zxoms官网查询接口兼容查询nb中大件
    @Override
    public void processNOrderNos(List<String> nOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        try {
            // 构建TmsWebOrderTrackVo参数
            TmsWebOrderTrackVo vo = new TmsWebOrderTrackVo();
            vo.setOrderList(nOrderNos);
            vo.setZip(zipInput);

            // 调用TmsCustomerOrderService.getZdjWebTrackNew()方法
            R result = customerOrderService.getZdjWebTrackNew(vo);

            if (result.getCode() == 0 && result.getData() != null) {
                // 转换getZdjWebTrackNew的返回格式为getTracksFromZxoms的统一格式
                convertZdjWebTrackToUnifiedFormat(result.getData(), ja, handledPkgNos, nOrderNos);
                log.info("N开头单号轨迹查询成功，订单号：{}", nOrderNos);
            } else {
                log.warn("N开头单号轨迹查询失败，订单号：{}，错误信息：{}", nOrderNos, result.getMsg());
            }
        } catch (Exception e) {
            log.error("N开头单号轨迹查询异常，订单号：{}", nOrderNos, e);
        }
    }


    /**
     * 转换统一格式
     * @param data getZdjWebTrackNew返回的数据
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param orderNos 订单号列表
     */
    private void convertZdjWebTrackToUnifiedFormat(Object data, JSONArray ja, Set<String> handledPkgNos, List<String> orderNos) {
        try {
            if (data instanceof Map) {
                Map<String, Object> response = (Map<String, Object>) data;
                Map<String, Object> result = (Map<String, Object>) response.get("result");
                Map<String, Object> uniTrackResult = (Map<String, Object>) response.get("uniTrackResult");

                // 优先处理result格式的数据
                boolean hasResultData = processResultFormat(result, ja, handledPkgNos, orderNos);

                // 如果result为空或没有数据，则尝试处理uniTrackResult格式
                if (!hasResultData && uniTrackResult != null && !uniTrackResult.isEmpty()) {
                    processUniTrackResultFormat(uniTrackResult, ja, handledPkgNos, orderNos);
                }
            }
        } catch (Exception e) {
            log.error("转换getZdjWebTrackNew返回格式异常", e);
        }
    }

    /**
     * 处理result格式的数据
     * @param result result数据
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param orderNos 订单号列表
     * @return 是否有数据被处理
     */
    private boolean processResultFormat(Map<String, Object> result, JSONArray ja, Set<String> handledPkgNos, List<String> orderNos) {
        if (result == null || result.isEmpty()) {
            return false;
        }

        boolean hasData = false;
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            String mainOrderNo = entry.getKey();
            Map<String, Object> mainOrderInfo = (Map<String, Object>) entry.getValue();
            List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

            if (CollUtil.isNotEmpty(subOrders)) {
                for (Map<String, Object> subOrder : subOrders) {
                    String subOrderNo = (String) subOrder.get("subOrderNo");

                    JSONObject orderJo = new JSONObject();
                    orderJo.set("pkgNo", subOrderNo);
                    orderJo.set("orderStatus", subOrder.get("orderStatus"));
                    orderJo.set("destination", subOrder.get("destination"));

                    // 转换轨迹格式
                    List<Map<String, Object>> trackList = (List<Map<String, Object>>) subOrder.get("trackList");
                    if (CollUtil.isNotEmpty(trackList)) {
                        JSONArray trackJa = trackList.stream()
                                .map(track -> {
                                    JSONObject jo = new JSONObject();
                                    jo.set("code", track.get("code"));
                                    jo.set("time", track.get("trackTime"));
                                    jo.set("status", track.get("status"));
                                    jo.set("city", track.get("city"));
                                    jo.set("content", track.get("trackDesc"));
                                    jo.set("timezone", track.get("timeZone"));
                                    return jo;
                                }).collect(Collectors.toCollection(JSONArray::new));
                        orderJo.set("track", trackJa);
                    }

                    // 处理签收图片
                    String signImgUrl = (String) subOrder.get("signImgUrl");
                    if (StrUtil.isNotBlank(signImgUrl)) {
                        orderJo.set("images", Arrays.asList(signImgUrl));
                    }

                    ja.add(orderJo);
                    handledPkgNos.add(subOrderNo);
                    if (!subOrderNo.equals(mainOrderNo)) {
                        handledPkgNos.add(mainOrderNo);
                    }
                    hasData = true;
                }
            }
        }
        return hasData;
    }

    /**
     * 处理uniTrackResult格式的数据
     * @param uniTrackResult uniTrackResult数据
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param orderNos 订单号列表
     */
    private void processUniTrackResultFormat(Map<String, Object> uniTrackResult, JSONArray ja, Set<String> handledPkgNos, List<String> orderNos) {
        for (Map.Entry<String, Object> entry : uniTrackResult.entrySet()) {
            String mainOrderNo = entry.getKey();
            Map<String, Object> orderTrackInfo = (Map<String, Object>) entry.getValue();
            List<Map<String, Object>> subOrders = (List<Map<String, Object>>) orderTrackInfo.get("subOrders");

            if (CollUtil.isNotEmpty(subOrders)) {
                for (Map<String, Object> subOrder : subOrders) {
                    String subOrderNo = (String) subOrder.get("subOrderNo");

                    JSONObject orderJo = new JSONObject();
                    orderJo.set("pkgNo", subOrderNo);
                    orderJo.set("orderStatus", subOrder.get("orderStatus"));

                    // uniTrackResult格式中可能没有destination字段，使用trackOperator作为替代
                    String destination = (String) subOrder.get("destination");
                    if (StrUtil.isBlank(destination)) {
                        destination = (String) subOrder.get("trackOperator");
                    }
                    orderJo.set("destination", destination);

                    // 转换轨迹格式 - uniTrackResult使用trackDetails字段
                    List<Map<String, Object>> trackDetails = (List<Map<String, Object>>) subOrder.get("trackDetails");
                    if (CollUtil.isNotEmpty(trackDetails)) {
                        JSONArray trackJa = trackDetails.stream()
                                .map(track -> {
                                    JSONObject jo = new JSONObject();
                                    jo.set("code", track.get("pathCode"));
                                    jo.set("time", track.get("pathTime"));
                                    jo.set("status", track.get("pathCode")); // 使用pathCode作为status
                                    jo.set("city", track.get("pathLocation"));
                                    jo.set("content", track.get("pathInfo"));
                                    jo.set("timezone", track.get("timezone"));
                                    return jo;
                                }).collect(Collectors.toCollection(JSONArray::new));
                        orderJo.set("track", trackJa);
                    }

                    // 处理签收图片 - 优先使用子订单的signImgUrl，如果为空则使用主订单的
                    String signImgUrl = (String) subOrder.get("signImgUrl");
                    if (StrUtil.isBlank(signImgUrl)) {
                        signImgUrl = (String) orderTrackInfo.get("signImgUrl");
                    }

                    if (StrUtil.isNotBlank(signImgUrl)) {
                        // 处理多个图片URL（逗号分隔）
                        String[] imageUrls = signImgUrl.split(",");
                        List<String> imageList = Arrays.stream(imageUrls)
                                .map(String::trim)
                                .filter(StrUtil::isNotBlank)
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(imageList)) {
                            orderJo.set("images", imageList);
                        }
                    }

                    ja.add(orderJo);
                    handledPkgNos.add(subOrderNo);
                    if (!subOrderNo.equals(mainOrderNo)) {
                        handledPkgNos.add(mainOrderNo);
                    }
                }
            }
        }
    }

    // ==================== 批量订单轨迹查询新接口实现 ====================

    /**
     * 批量订单轨迹查询 - 新接口（支持限流）
     * @param orderNos 订单号列表
     * @return 批量轨迹查询结果
     */
    @Override
    public R batchTrackOrders(List<String> orderNos) {
        if (CollUtil.isEmpty(orderNos)) {
            return R.failed("订单号列表不能为空");
        }

        // 限制批量查询数量
        int maxBatchSize = 50;
        if (orderNos.size() > maxBatchSize) {
            return R.failed("批量查询订单数量不能超过" + maxBatchSize + "个");
        }

        log.info("批量轨迹查询开始，订单数量：{}", orderNos.size());

        // 去重和过滤空值
        List<String> validOrderNos = orderNos.stream()
                .filter(StrUtil::isNotBlank)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(validOrderNos)) {
            return R.failed("有效订单号列表为空");
        }

        // 批量处理订单轨迹查询
        Map<String, List<TmsApiOrderTaskDto>> result = processBatchOrders(validOrderNos);

        log.info("批量轨迹查询完成，查询订单数：{}，返回结果数：{}", validOrderNos.size(), result.size());
        return R.ok(result);
    }

    /**
     * 批量处理订单轨迹查询
     * 优化为批量查询，减少数据库查询次数
     */
    private Map<String, List<TmsApiOrderTaskDto>> processBatchOrders(List<String> orderNos) {
        if (CollUtil.isEmpty(orderNos)) {
            return Collections.emptyMap();
        }

        // 批量查询订单信息（按跟踪单号查询）
        List<TmsCustomerOrderEntity> customerOrdersByEntrusted = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos)
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0"));

        // 批量查询主单信息（按客户单号查询，补充单号未查到的）
        Set<String> foundEntrustedOrderNos = customerOrdersByEntrusted.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toSet());
        List<String> notFoundOrderNos = orderNos.stream()
                .filter(orderNo -> !foundEntrustedOrderNos.contains(orderNo))
                .collect(Collectors.toList());

        List<TmsCustomerOrderEntity> customerOrdersByCustomer = Collections.emptyList();
        if (!notFoundOrderNos.isEmpty()) {
            customerOrdersByCustomer = customerOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getCustomerOrderNumber, notFoundOrderNos)
                            .eq(TmsCustomerOrderEntity::getDelFlag, "0"));
        }

        // 合并查询结果
        List<TmsCustomerOrderEntity> allCustomerOrders = new ArrayList<>();
        allCustomerOrders.addAll(customerOrdersByEntrusted);
        allCustomerOrders.addAll(customerOrdersByCustomer);

        if (CollUtil.isEmpty(allCustomerOrders)) {
            log.debug("批量查询订单不存在：{}", orderNos);
            return Collections.emptyMap();
        }

        // 获取所有订单单号
        List<String> allOrderNos = allCustomerOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询所有子单
        List<TmsCustomerOrderEntity> allSubOrders = new ArrayList<>();
        for (String orderNo : allOrderNos) {
            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                            .eq(TmsCustomerOrderEntity::getSubFlag, true)
                            .eq(TmsCustomerOrderEntity::getDelFlag, "0"));
            allSubOrders.addAll(subOrders);
        }

        if (CollUtil.isEmpty(allSubOrders)) {
            return Collections.emptyMap();
        }

        // 获取所有子单号
        List<String> allSubOrderNos = allSubOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toList());

        // 批量查询所有子单轨迹
        List<TmsOrderTrackEntity> allOrderTrackList = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .in(TmsOrderTrackEntity::getOrderNo, allSubOrderNos)
                        .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                        .orderByDesc(TmsOrderTrackEntity::getAddTime));

        // 将轨迹按单号分组
        Map<String, List<TmsOrderTrackEntity>> allTrackMap = allOrderTrackList.stream()
                .collect(Collectors.groupingBy(TmsOrderTrackEntity::getOrderNo));

        // 最终返回的轨迹数据
        Map<String, List<TmsApiOrderTaskDto>> finalTrackMap = new LinkedHashMap<>();

        // 直接遍历所有子单，构建轨迹数据
        for (TmsCustomerOrderEntity subOrder : allSubOrders) {
            String subOrderNo = subOrder.getEntrustedOrderNumber();
            List<TmsOrderTrackEntity> tracks = allTrackMap.getOrDefault(subOrderNo, Collections.emptyList());

            List<TmsApiOrderTaskDto> trackDtos = tracks.stream()
                    .map(entity -> {
                        TmsApiOrderTaskDto dto = new TmsApiOrderTaskDto();
                        BeanUtils.copyProperties(entity, dto);
                        dto.setLocationDescription(entity.getExternalDescription());
                        dto.setZip(entity.getPostalCode());
                        dto.setPathCode(entity.getStatusCode());
                        return dto;
                    })
                    .collect(Collectors.toList());

            finalTrackMap.put(subOrderNo, trackDtos);
        }

        return finalTrackMap;
    }


    // ==================== Temu客户订单轨迹推送功能实现 ====================

    /**
     * Temu客户订单轨迹推送接口
     * @param pushNodeMsgList 轨迹推送消息列表
     * @return 推送结果
     */
    @Override
    public R pushTemuTrackData(List<PushNodeMsgDto> pushNodeMsgList) {
        if (CollUtil.isEmpty(pushNodeMsgList)) {
            return R.failed("推送数据列表不能为空");
        }

        log.info("开始执行Temu客户订单轨迹推送，数据量：{}", pushNodeMsgList.size());

        int successCount = 0;
        int failureCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (PushNodeMsgDto pushNodeMsg : pushNodeMsgList) {
            try {
                // 验证必要字段
                if (StrUtil.isBlank(pushNodeMsg.getWno())) {
                    errorMessages.add("运单号不能为空");
                    failureCount++;
                    continue;
                }

                // 构建Temu API轨迹数据格式
                Map<String, Object> trackData = buildTemuTrackData(pushNodeMsg);

                // 调用Temu API推送
                TemuApiClient.TemuApiResponse response = temuApiClient.pushTrackData(
                        pushNodeMsg.getCno(), // 客户单号作为packageSn
                        pushNodeMsg.getWno(), // 运单号作为trackingNumber
                        Collections.singletonList(trackData)
                );

                if (response.isSuccess()) {
                    successCount++;
                    log.info("Temu轨迹推送成功 - 运单号: {}, 客户单号: {}", pushNodeMsg.getWno(), pushNodeMsg.getCno());
                } else {
                    failureCount++;
                    String errorMsg = String.format("运单号: %s 推送失败: %s", pushNodeMsg.getWno(), response.getMessage());
                    errorMessages.add(errorMsg);
                    log.error(errorMsg);
                }

            } catch (Exception e) {
                failureCount++;
                String errorMsg = String.format("运单号: %s 推送异常: %s", pushNodeMsg.getWno(), e.getMessage());
                errorMessages.add(errorMsg);
                log.error(errorMsg, e);
            }
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", pushNodeMsgList.size());
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        if (!errorMessages.isEmpty()) {
            result.put("errorMessages", errorMessages);
        }

        log.info("Temu客户订单轨迹推送完成 - 总数: {}, 成功: {}, 失败: {}",
                pushNodeMsgList.size(), successCount, failureCount);

        return R.ok(result);
    }

    /**
     * 查询待推送的Temu客户订单轨迹数据
     * @return 待推送的轨迹数据列表
     */
    public R queryPendingTemuTrackData() {
        try {
            log.info("开始查询待推送的Temu客户订单轨迹数据");

            // 查询未推送的轨迹事件(筛选Temu客户订单(秋礼说暂时使用id判断，后续如有提供相关的token在更改))
            LambdaQueryWrapper<TmsTrackWebhookEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsTrackWebhookEntity::getIsPush, 0);
            wrapper.eq(TmsTrackWebhookEntity::getCustomerId, Arrays.asList(60L, 66L));
            List<TmsTrackWebhookEntity> unpushedEvents = tmsTrackWebhookService.list(wrapper);

            if (CollUtil.isEmpty(unpushedEvents)) {
                log.info("没有待推送的轨迹事件");
                return R.ok(Collections.emptyList());
            }
            log.info("查询到未推送轨迹事件数量: {}", unpushedEvents.size());

            // 构建推送数据列表
            List<PushNodeMsgDto> pushNodeMsgList = new ArrayList<>();
            for (TmsTrackWebhookEntity order : unpushedEvents) {
                // 查找对应的轨迹事件
                TmsOrderTrackText trackEvent = tmsTrackTextService.getById(order.getTrackId());

                if (trackEvent != null) {
                    PushNodeMsgDto pushNodeMsg = buildPushNodeMsgFromOrder(order, trackEvent);
                    if (pushNodeMsg != null) {
                        pushNodeMsgList.add(pushNodeMsg);
                    }
                }
            }

            log.info("构建待推送数据完成，数量: {}", pushNodeMsgList.size());
            return R.ok(pushNodeMsgList);

        } catch (Exception e) {
            log.error("查询待推送Temu客户订单轨迹数据异常", e);
            return R.failed("查询待推送数据异常: " + e.getMessage());
        }
    }

    /**
     * 构建Temu API轨迹数据格式
     */
    private Map<String, Object> buildTemuTrackData(PushNodeMsgDto pushNodeMsg) {
        Map<String, Object> trackData = new HashMap<>();

        // 轨迹描述
        trackData.put("description", StrUtil.blankToDefault(pushNodeMsg.getPathInfo(), ""));
        trackData.put("tailDescription", StrUtil.blankToDefault(pushNodeMsg.getPathInfo(), ""));

        // 地理位置信息
        trackData.put("eventCity", "");
        trackData.put("eventCountry", StrUtil.blankToDefault(pushNodeMsg.getCountry(), ""));
        trackData.put("eventLocation", StrUtil.blankToDefault(pushNodeMsg.getPathLocation(), ""));
        trackData.put("eventProvince", "");

        // 轨迹节点代码
        trackData.put("eventCode", pushNodeMsg.getPathCode() != null ? pushNodeMsg.getPathCode().toString() : "");

        // 航班号
        trackData.put("flightNo", "");

        // 操作时间
        if (pushNodeMsg.getPathDate() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            trackData.put("operationTime", pushNodeMsg.getPathDate().format(formatter));
        } else {
            trackData.put("operationTime", "");
        }

        // 时区
        trackData.put("timeZone", StrUtil.blankToDefault(pushNodeMsg.getTimeZone(), "GMT+08:00"));

        // 转运标志和信息
        trackData.put("transferFlag", 1);
        trackData.put("transferNo", StrUtil.blankToDefault(pushNodeMsg.getWno(), ""));
        trackData.put("transferService", "JYGJEXP");

        return trackData;
    }

    /**
     * 从订单和轨迹事件构建推送消息对象
     */
    private PushNodeMsgDto buildPushNodeMsgFromOrder(TmsTrackWebhookEntity order, TmsOrderTrackText trackEvent) {
        try {
            PushNodeMsgDto pushNodeMsg = new PushNodeMsgDto();

            // 基本订单信息
            pushNodeMsg.setCid(order.getCustomerId());
            pushNodeMsg.setOrderId(order.getId());
            pushNodeMsg.setWno(order.getOrderNo());

            // 轨迹信息 - 从TmsOrderTrackText的tracking字段解析JSON获取
            if (trackEvent != null && StrUtil.isNotBlank(trackEvent.getTracking())) {
                try {
                    // 解析tracking字段的JSON数据
                    JSONObject trackingJson = JSONUtil.parseObj(trackEvent.getTracking());

                    // tracking字段格式: {"N2527D0000133001": [轨迹数组]}
                    // 获取订单号对应的轨迹数组
                    JSONArray trackArray = null;
                    for (String key : trackingJson.keySet()) {
                        if (key.equals(order.getOrderNo()) || key.startsWith(order.getOrderNo())) {
                            trackArray = trackingJson.getJSONArray(key);
                            break;
                        }
                    }

                    if (trackArray != null && !trackArray.isEmpty()) {
                        // 获取最新的轨迹信息（数组最后一个元素）
                        JSONObject latestTrack = trackArray.getJSONObject(trackArray.size() - 1);

                        // 设置轨迹信息
                        pushNodeMsg.setCno("");
                        pushNodeMsg.setWno(order.getOrderNo()); // 使用订单号作为客户单号
                        pushNodeMsg.setPathCode(latestTrack.getInt("pathCode"));

                        // 处理时间字段
                        Long addTimeMillis = latestTrack.getLong("addTime");
                        if (addTimeMillis != null) {
                            pushNodeMsg.setPathDate(LocalDateTime.ofInstant(
                                    java.time.Instant.ofEpochMilli(addTimeMillis),
                                    java.time.ZoneId.systemDefault()));
                        }

                        pushNodeMsg.setPathInfo(latestTrack.getStr("locationDescription"));
                        pushNodeMsg.setPathLocation(latestTrack.getStr("city"));
                        pushNodeMsg.setCountry(latestTrack.getStr("country"));
                        pushNodeMsg.setTimeZone(StrUtil.blankToDefault(latestTrack.getStr("timeZone"), "GMT+08:00"));
                    } else {
                        log.warn("未找到订单号对应的轨迹数组 - 订单号: {}", order.getOrderNo());
                    }

                } catch (Exception jsonException) {
                    log.error("解析tracking字段JSON异常 - 订单号: {}, tracking内容: {}", order.getOrderNo(), trackEvent.getTracking(), jsonException);
                }
            } else {
                log.warn("轨迹事件为空或tracking字段为空 - 订单号: {}", order.getOrderNo());
            }
            // 消息投递时间
            pushNodeMsg.setSendMsgDate(LocalDateTime.now());
            return pushNodeMsg;
        } catch (Exception e) {
            log.error("构建推送消息对象异常 - 订单号: {}", order.getOrderNo(), e);
            return null;
        }
    }

    /**
     * 执行Temu轨迹推送任务（供定时任务调用）
     * @return 推送结果
     */
    public R executeTemuTrackPushTask() {
        try {
            log.info("开始执行Temu轨迹推送定时任务");

            // 查询待推送数据
            R queryResult = queryPendingTemuTrackData();
            if (!queryResult.isOk() || queryResult.getData() == null) {
                log.info("没有待推送的数据或查询失败");
                return R.ok("没有待推送的数据");
            }

            @SuppressWarnings("unchecked") //蔽编译器警告
            List<PushNodeMsgDto> pushNodeMsgList = (List<PushNodeMsgDto>) queryResult.getData();

            if (CollUtil.isEmpty(pushNodeMsgList)) {
                log.info("待推送数据列表为空");
                return R.ok("没有待推送的数据");
            }

            // 执行推送
            R pushResult = pushTemuTrackData(pushNodeMsgList);

            // 更新推送状态
            if (pushResult.isOk() && pushResult.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultData = (Map<String, Object>) pushResult.getData();
                Integer successCount = (Integer) resultData.get("successCount");

                // 更新成功推送的记录状态
                updatePushStatusForSuccessfulRecords(pushNodeMsgList, successCount);
            }

            log.info("Temu轨迹推送定时任务执行完成");
            return pushResult;

        } catch (Exception e) {
            log.error("执行Temu轨迹推送定时任务异常", e);
            return R.failed("推送任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 更新成功推送记录的状态
     */
    private void updatePushStatusForSuccessfulRecords(List<PushNodeMsgDto> pushNodeMsgList, Integer successCount) {
        if (successCount == null || successCount <= 0) {
            return;
        }

        try {
            // 查询对应的轨迹事件并更新状态
            List<String> orderNos = pushNodeMsgList.stream()
                    .map(PushNodeMsgDto::getWno)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());

            List<TmsTrackWebhookEntity> events = tmsTrackWebhookService.list(new LambdaQueryWrapper<TmsTrackWebhookEntity>()
                    .in(TmsTrackWebhookEntity::getOrderNo, orderNos)
                    .eq(TmsTrackWebhookEntity::getIsPush, 0)
            );

            // 更新前successCount条记录的状态
            int updateCount = Math.min(successCount, events.size());
            for (int i = 0; i < updateCount; i++) {
                TmsTrackWebhookEntity event = events.get(i);

                // 更新推送状态
                TmsTrackWebhookEntity tmsOrderTrackText = new TmsTrackWebhookEntity();
                tmsOrderTrackText.setId(event.getId());
                tmsOrderTrackText.setUpdateTime(LocalDateTime.now());
                tmsOrderTrackText.setIsPush(1);
                tmsOrderTrackText.setPushStatus("推送成功");
                tmsTrackWebhookService.updateById(tmsOrderTrackText);
            }

            log.info("更新推送状态完成，成功更新记录数: {}", updateCount);

        } catch (Exception e) {
            log.error("更新推送状态异常", e);
        }
    }

}
