package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsBaseCityStreetPostcodeEntity;
import com.jygjexp.jynx.tms.service.TmsBaseCityStreetPostcodeService;
import com.jygjexp.jynx.tms.vo.AddressAutocompleteVo;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Collections;
import java.util.stream.Collectors;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import cn.hutool.core.util.StrUtil;

/**
 * 加拿大邮编地址库
 *
 * <AUTHOR>
 * @date 2025-09-02 16:17:40
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBaseCityStreetPostcode" )
@Tag(description = "tmsBaseCityStreetPostcode" , name = "加拿大邮编地址库管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBaseCityStreetPostcodeController {

    private final TmsBaseCityStreetPostcodeService tmsBaseCityStreetPostcodeService;

    /**
     * 基于MySQL的地址补全接口
     * @param postcode 六位邮编（必填）
     * @param keyword 搜索关键词（可选）
     * @param limit 返回条数限制（必填，最大值30）
     * @return 匹配的地址列表
     */
    @Operation(summary = "地址补全", description = "基于MySQL的地址补全功能，支持邮编和关键词搜索")
    @GetMapping("/autocomplete")
    public R<List<AddressAutocompleteVo>> addressAutocomplete(@RequestParam("postcode") String postcode,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam("limit") Integer limit) {

        // 参数校验
        if (StrUtil.isBlank(postcode)) {
            return R.ok(Collections.emptyList());
        }

        if (postcode.length() < 3) {
            return R.ok(Collections.emptyList());
        }

        if (limit == null || limit <= 0) {
            return R.ok(Collections.emptyList());
        }

        if (limit > 30) {
            return R.ok(Collections.emptyList());
        }

        List<TmsBaseCityStreetPostcodeEntity> entities = tmsBaseCityStreetPostcodeService
                .searchAddressAutocomplete(postcode, keyword, limit);

        // 转换为VO对象
        List<AddressAutocompleteVo> result = entities.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        return R.ok(result);
    }

    /**
     * 实体转换为VO
     */
    private AddressAutocompleteVo convertToVo(TmsBaseCityStreetPostcodeEntity entity) {
        AddressAutocompleteVo vo = new AddressAutocompleteVo();
        vo.setId(entity.getId());
        vo.setThreePostCode(entity.getThreePostCode());
        vo.setSixPostCode(entity.getSixPostCode());
        vo.setCity(entity.getCity());
        vo.setProvince(entity.getProvince());
        vo.setStreet(entity.getRstreet());

        // 构建完整地址
        StringBuilder fullAddress = new StringBuilder();
        if (StrUtil.isNotBlank(entity.getRstreet())) {
            fullAddress.append(entity.getRstreet()).append(", ");
        }
        if (StrUtil.isNotBlank(entity.getCity())) {
            fullAddress.append(entity.getCity()).append(", ");
        }
        if (StrUtil.isNotBlank(entity.getProvince())) {
            fullAddress.append(entity.getProvince()).append(" ");
        }
        if (StrUtil.isNotBlank(entity.getSixPostCode())) {
            fullAddress.append(entity.getSixPostCode());
        }
        vo.setFullAddress(fullAddress.toString());

        // 设置匹配地址类型
        vo.setMatchType("Address");

        return vo;
    }

//    private final AddressSearchService addressSearchService;
//
//    /**
//     * 地址自动补全API
//     * @param keyword 搜索关键词（支持邮编/城市/街道）
//     * @param limit 返回结果的最大条数（必需参数）
//     * @return 匹配的地址列表
//     */
//    @Operation(summary = "地址自动补全", description = "根据关键词搜索匹配的地址信息，支持邮编、城市、街道的模糊匹配")
//    @GetMapping("/autocomplete")
//    public R<List<AddressAutocompleteVo>> addressAutocomplete(@RequestParam("keyword") String keyword, @RequestParam("limit") Integer limit) {
//
//        // 参数校验
//        if (StrUtil.isBlank(keyword)) {
//            return R.failed("搜索关键词不能为空");
//        }
//
//        if (limit == null || limit <= 0) {
//            return R.failed("返回条数必须大于0");
//        }
//
//        if (limit > 50) {
//            return R.failed("返回条数不能超过50");
//        }
//
//        List<TmsBaseCityStreetPostcodeEntity> entities = tmsBaseCityStreetPostcodeService
//                .searchAddressAutocomplete(keyword, limit);
//
//        // 转换为VO对象
//        List<AddressAutocompleteVo> result = entities.stream()
//                .map(this::convertToVo)
//                .collect(Collectors.toList());
//
//        return R.ok(result);
//    }
//
//    /**
//     * 实体转换为VO
//     */
//    private AddressAutocompleteVo convertToVo(TmsBaseCityStreetPostcodeEntity entity) {
//        AddressAutocompleteVo vo = new AddressAutocompleteVo();
//        vo.setId(entity.getId());
//        vo.setThreePostCode(entity.getThreePostCode());
//        vo.setSixPostCode(entity.getSixPostCode());
//        vo.setCity(entity.getCity());
//        vo.setProvince(entity.getProvince());
//        vo.setStreet(entity.getRstreet());
//
//        // 构建完整地址
//        StringBuilder fullAddress = new StringBuilder();
//        if (StrUtil.isNotBlank(entity.getRstreet())) {
//            fullAddress.append(entity.getRstreet()).append(", ");
//        }
//        if (StrUtil.isNotBlank(entity.getCity())) {
//            fullAddress.append(entity.getCity()).append(", ");
//        }
//        if (StrUtil.isNotBlank(entity.getProvince())) {
//            fullAddress.append(entity.getProvince()).append(" ");
//        }
//        if (StrUtil.isNotBlank(entity.getSixPostCode())) {
//            fullAddress.append(entity.getSixPostCode());
//        }
//        vo.setFullAddress(fullAddress.toString());
//
//        // 设置匹配地址类型
//        vo.setMatchType("Address");
//
//        return vo;
//    }
//
//    /**
//     * 同步数据到Elasticsearch
//     * @return 同步结果
//     */
//    @Operation(summary = "同步数据到Elasticsearch", description = "将数据库中的地址数据同步到Elasticsearch索引")
//    @PostMapping("/sync-to-elasticsearch")
//    public R<String> syncToElasticsearch() {
//        try {
//            tmsBaseCityStreetPostcodeService.syncDataToElasticsearch();
//            return R.ok("数据同步成功");
//        } catch (Exception e) {
//            return R.failed("数据同步失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 验证数据一致性（调试用）
//     * @param keyword 测试关键词
//     * @return 验证结果
//     */
//    @Operation(summary = "验证数据一致性", description = "验证ES和数据库数据的一致性，用于调试")
//    @GetMapping("/validate-data")
//    public R<String> validateData(@RequestParam("keyword") String keyword) {
//        try {
//            tmsBaseCityStreetPostcodeService.validateDataConsistency(keyword);
//            return R.ok("数据一致性验证完成，请查看日志");
//        } catch (Exception e) {
//            return R.failed("数据一致性验证失败: " + e.getMessage());
//        }
//    }
//
//    // 查询所有数据
//    @GetMapping("/es/all")
//    public List<AddressDocument> getAllFromEs() {
//        return addressSearchService.findAll();
//    }
//
//    // 按关键字查询
//    @GetMapping("/es/search")
//    public List<AddressDocument> searchFromEs(@RequestParam String keyword) {
//        return addressSearchService.searchByKeyword(keyword);
//    }


/*    *//**
     * 分页查询
     * @param page 分页对象
     * @param tmsBaseCityStreetPostcode 加拿大邮编地址库
     * @return
     *//*
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_view')" )
    public R getTmsBaseCityStreetPostcodePage(@ParameterObject Page page, @ParameterObject TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode) {
        LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsBaseCityStreetPostcodeService.page(page, wrapper));
    }


    *//**
     * 通过id查询加拿大邮编地址库
     * @param id id
     * @return R
     *//*
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(tmsBaseCityStreetPostcodeService.getById(id));
    }

    *//**
     * 新增加拿大邮编地址库
     * @param tmsBaseCityStreetPostcode 加拿大邮编地址库
     * @return R
     *//*
    @Operation(summary = "新增加拿大邮编地址库" , description = "新增加拿大邮编地址库" )
    @SysLog("新增加拿大邮编地址库" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_add')" )
    public R save(@RequestBody TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode) {
        return R.ok(tmsBaseCityStreetPostcodeService.save(tmsBaseCityStreetPostcode));
    }

    *//**
     * 修改加拿大邮编地址库
     * @param tmsBaseCityStreetPostcode 加拿大邮编地址库
     * @return R
     *//*
    @Operation(summary = "修改加拿大邮编地址库" , description = "修改加拿大邮编地址库" )
    @SysLog("修改加拿大邮编地址库" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_edit')" )
    public R updateById(@RequestBody TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode) {
        return R.ok(tmsBaseCityStreetPostcodeService.updateById(tmsBaseCityStreetPostcode));
    }

    *//**
     * 通过id删除加拿大邮编地址库
     * @param ids id列表
     * @return R
     *//*
    @Operation(summary = "通过id删除加拿大邮编地址库" , description = "通过id删除加拿大邮编地址库" )
    @SysLog("通过id删除加拿大邮编地址库" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(tmsBaseCityStreetPostcodeService.removeBatchByIds(CollUtil.toList(ids)));
    }*/


    /**
     * 导出excel 表格
     * @param tmsBaseCityStreetPostcode 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
/*    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_export')" )
    public List<TmsBaseCityStreetPostcodeEntity> export(TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode,Integer[] ids) {
        return tmsBaseCityStreetPostcodeService.list(Wrappers.lambdaQuery(tmsBaseCityStreetPostcode).in(ArrayUtil.isNotEmpty(ids), TmsBaseCityStreetPostcodeEntity::getId, ids));
    }*/
}